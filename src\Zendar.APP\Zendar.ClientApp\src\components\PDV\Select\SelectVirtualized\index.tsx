import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';

import SelectPadrao from 'components/PDV/Select/SelectPadrao';

import OptionType from 'types/optionType';

import { MenuList } from './components/MenuList';
import {
  SelectVirtualizedProps,
  valuePaginationDefault,
} from './validationForms';

export type OptionProps = OptionType & {
  isTooltip: boolean;
  labelTooltip: string;
};

export const SelectVirtualized = ({
  name,
  components,
  totalRegistros = 0,
  autoFocus,
  handleGetOptions,
  placeholder,
  isClearable,
  withoutDefaultOptions = true,
  clearOptionsOnBlur = true,
  onBlur = () => {},
  ...rest
}: SelectVirtualizedProps) => {
  const { watch } = useFormContext();

  const [valueInput, setValueInput] = useState('');
  const [listOptions, setOptions] = useState<OptionProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const campo = watch(`${name}`);

  const listOptionsRef = useRef<OptionProps[]>([]);
  const isLoadingRef = useRef(false);
  const inputTypingTimeoutRef = useRef<NodeJS.Timeout>();
  const paginaAtualRef = useRef(1);
  const pesquisaAnteriorRef = useRef<string>('');

  const missingOptionsLoad = listOptions.length < totalRegistros;

  const alterarValorInput = (inputValue: string) => {
    setValueInput(inputValue);
  };

  const clearList = useCallback(() => {
    paginaAtualRef.current = 1;
    setOptions([]);
  }, []);

  const formatOptionLabel = (option: OptionType, maxCharacters = 76) => {
    const label = option.label;
    const labelCortada = label.slice(0, maxCharacters);
    const excedeLimiteTexto = label.length > maxCharacters;

    return {
      ...option,
      label: excedeLimiteTexto
        ? labelCortada.charAt(0).toUpperCase() +
          labelCortada.slice(1).toLowerCase()
        : label,
      labelTooltip: label,
      isTooltip: excedeLimiteTexto,
    };
  };

  const loadOptions = useCallback(
    async (inputValue: string) => {
      clearTimeout(inputTypingTimeoutRef.current);
      setIsLoading(true);

      if (inputValue !== pesquisaAnteriorRef.current) {
        paginaAtualRef.current = 1;
        pesquisaAnteriorRef.current = inputValue;
        setOptions([]);
      }

      return new Promise<OptionType[]>(async (resolve) => {
        inputTypingTimeoutRef.current = setTimeout(async () => {
          try {
            const newOptions = await handleGetOptions(inputValue, {
              ...valuePaginationDefault,
              currentPage: paginaAtualRef.current,
              pageSize: valuePaginationDefault.pageSize,
            });

            const opcoesComLabelsFormatadas = newOptions.map((option) =>
              formatOptionLabel(option)
            );

            setOptions((prev) => [...prev, ...opcoesComLabelsFormatadas]);
            paginaAtualRef.current++;
            resolve(opcoesComLabelsFormatadas);
          } catch (error) {
            resolve([]);
          } finally {
            setIsLoading(false);
          }
        }, 500);
      });
    },
    [handleGetOptions, setOptions]
  );

  const updateOptionsListSelect = useCallback(
    async (inputValue: string) => {
      await loadOptions(inputValue);
    },
    [loadOptions]
  );

  const componentsSelect = useMemo(() => {
    return {
      ...components,
      MenuList: ({ children }: any) =>
        MenuList({
          isLoading: isLoadingRef.current,
          children,
          listOptions: listOptionsRef.current,
          totalRegistros,
          inputValue: valueInput,
          updateOptionsListSelect,
        }),
    };
  }, [components, totalRegistros, updateOptionsListSelect, valueInput]);

  const buscarOpcoes = useCallback(() => {
    updateOptionsListSelect(valueInput);

    clearList();
  }, [clearList, updateOptionsListSelect, valueInput]);

  useEffect(() => {
    if ((!withoutDefaultOptions && !campo) || valueInput?.length > 0) {
      buscarOpcoes();
    }
  }, [campo, buscarOpcoes, valueInput, withoutDefaultOptions]);

  useEffect(() => {
    listOptionsRef.current = listOptions;
  }, [listOptions]);

  return (
    <SelectPadrao
      name={name}
      autoFocus={autoFocus}
      isClearable={isClearable}
      isLoading={isLoading && missingOptionsLoad}
      placeholder={placeholder}
      onInputChange={alterarValorInput}
      filterOption={() => true}
      components={componentsSelect}
      options={listOptions}
      onMenuClose={() => {}}
      onBlur={(e) => {
        if (clearOptionsOnBlur) {
          clearList();
        }

        onBlur(e);
      }}
      {...rest}
    />
  );
};
