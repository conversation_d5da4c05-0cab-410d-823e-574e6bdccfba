// @ts-nocheck
import { useCallback } from 'react';

import VirtualizedList from 'components/update/VirtualizedList';

type OptionsProps = { label: string; value: string }[];

export type MenuListProps = {
  isLoading: boolean;
  inputValue: string;
  listOptions: OptionsProps;
  totalRegistros: number;
  children: ChildrenProps[] | ChildrenProps;
  updateOptionsListSelect: (inputValue: string) => Promise<void>;
  initialScrollTop: number;
  onScrollChange: (y: number) => void;
};

type ChildrenProps = {
  props: {
    value: string;
  };
};

export const MenuList = ({
  isLoading,
  totalRegistros,
  listOptions,
  children,
  inputValue,
  updateOptionsListSelect,
  initialScrollTop,
  onScrollChange,
}: MenuListProps) => {
  const missingOptionsLoad = listOptions.length < totalRegistros;

  const listRenderVirtualized = (options: OptionsProps) => {
    const listInfoSelect = children as ChildrenProps[];

    if (!Array.isArray(listInfoSelect)) {
      return [];
    }

    const newOption = [...listInfoSelect]
      .map((itemChildren) => {
        const itemVirtualized = options?.find(
          (item) => item?.value === itemChildren?.props?.value
        );

        if (itemVirtualized) {
          return itemChildren;
        }

        return undefined;
      })
      .filter(Boolean);

    return newOption;
  };

  const getNewOptions = useCallback(
    async (isObserver: boolean) => {
      console.log('📋 MenuList getNewOptions:', {
        isObserver,
        missingOptionsLoad,
        isLoading,
        inputValue,
        listOptionsLength: listOptions.length,
        totalRegistros,
        timestamp: new Date().toISOString(),
      });
      if (isObserver && missingOptionsLoad && !isLoading) {
        console.log('✅ MenuList: Calling updateOptionsListSelect');
        await updateOptionsListSelect(inputValue);
      } else {
        console.log('❌ MenuList: Not calling updateOptionsListSelect');
      }
    },
    [inputValue, isLoading, missingOptionsLoad, updateOptionsListSelect]
  );

  return (
    <VirtualizedList
      containerHeight={isLoading ? 0 : 200}
      itemHeight={32}
      sx={{
        borderWidth: '1px',
        borderColor: 'gray.100',
        borderRadius: '6px',
        boxShadow: '0px 0px 6px #00000034',
        maxH: '200px',
        overflowY: 'auto',
        opacity: isLoading && missingOptionsLoad ? 0.5 : 1,
      }}
      colorBgGradient="gray.50"
      handleOnClick={async (_, observer) => {
        await getNewOptions(observer);
      }}
      isLoading={isLoading && missingOptionsLoad}
      marginBottom={5}
      items={listOptions}
      quantidadeItems={totalRegistros}
      render={listRenderVirtualized}
      totalRegistros={totalRegistros}
      initialScrollTop={initialScrollTop}
      onScrollChange={onScrollChange}
    />
  );
};
