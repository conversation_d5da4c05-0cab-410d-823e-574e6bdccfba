import { Box, CSSObject, Flex } from '@chakra-ui/react';
import {
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
  useEffect,
  useLayoutEffect,
} from 'react';

import { useIntersectionObserver } from 'hooks/useIntersectionObserver';

export type VirtualizedListRefProps = {
  reload: () => void;
};

export type PaginationVirtualizedListProps = {
  paginaAtual: number;
  tamanhoPagina: number;
  campoOrdenacao: string;
  direcaoOrdenacao: 'asc' | 'desc';
};

type VirtualizedListProps<T> = {
  items: T[];
  render: (items: T[], heigh: string, marginBottom?: string) => React.ReactNode;
  itemHeight: number;
  containerHeight: number;
  quantidadeItems: number;
  handleOnClick?: (
    data: PaginationVirtualizedListProps,
    functionIsObserver: boolean
  ) => Promise<void>;
  colorBgGradient?: string;
  tamanhoPagina?: number;
  direcaoOrdenacao?: string;
  campoOrdenacao?: string;
  marginBottom?: number;
  sx?: CSSObject | undefined;
  noItemsMessage?: string;
  isLoading?: boolean;
  totalRegistros?: number;
  initialScrollTop?: number;
  onScrollChange?: (y: number) => void;
};

export const pagination = {
  tamanhoPagina: 10,
  direcaoOrdenacao: 'asc',
  campoOrdenacao: '',
};

function List<T>(
  {
    items,
    itemHeight: valueItemHeight,
    marginBottom = 0,
    containerHeight,
    quantidadeItems,
    sx,
    tamanhoPagina = 10,
    direcaoOrdenacao = 'asc',
    campoOrdenacao = '',
    render,
    handleOnClick,
    colorBgGradient = 'gray.100',
    noItemsMessage = 'Nenhum item foi encontrado',
    isLoading,
    totalRegistros,
    initialScrollTop = 0,
    onScrollChange = () => {},
  }: VirtualizedListProps<T>,
  ref: React.ForwardedRef<VirtualizedListRefProps>
) {
  const [scrollTop, setScrollTop] = useState(0);

  const paginaAtualRef = useRef(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const itemHeight = valueItemHeight + marginBottom;

  const visibleItemsLength = Math.ceil(containerHeight / itemHeight) + 1;

  const startIndex = Math.min(
    Math.floor(scrollTop / itemHeight),
    Math.max(items.length - visibleItemsLength, 0)
  );
  const endIndex = Math.min(startIndex + visibleItemsLength, items.length);

  const visibleItems = items.slice(startIndex, endIndex);

  const hasUserScrolledRef = useRef(false);
  const lastLoadAtScrollRef = useRef<number | null>(null);
  const getEl = () => containerRef.current;

  const nearBottom = () => {
    const el = getEl();
    if (!el) return false;

    const loadedHeight = items.length * itemHeight;
    const preload = Math.min(containerHeight * 0.6, 500);
    const maxHeight = Math.min(loadedHeight, el.scrollHeight);

    return el.scrollTop + el.clientHeight + preload >= maxHeight;
  };

  const canLoadMore = () => {
    if (isLoading) return false;
    if (typeof totalRegistros === 'number' && items.length >= totalRegistros)
      return false;

    const el = getEl();
    if (!el) return false;

    if (el.scrollTop === 0 && !hasUserScrolledRef.current) return false;

    if (!nearBottom()) return false;

    if (lastLoadAtScrollRef.current === el.scrollTop) return false;

    return true;
  };

  const preservedScrollRef = useRef<number | null>(null);

  const chamarFuncaoPaginada = async (
    nextPage: boolean,
    isObserver: boolean
  ) => {
    if (nextPage) {
      paginaAtualRef.current += 1;
    }

    const dataPagination = {
      tamanhoPagina,
      direcaoOrdenacao,
      campoOrdenacao,
      paginaAtual: nextPage ? paginaAtualRef.current : 1,
    } as PaginationVirtualizedListProps;

    preservedScrollRef.current = containerRef.current?.scrollTop ?? 0;

    if (handleOnClick) {
      await handleOnClick(dataPagination, isObserver);
    }
  };

  const tryLoadNext = async (isObserver: boolean) => {
    if (!canLoadMore()) return;
    lastLoadAtScrollRef.current = getEl()?.scrollTop ?? 0;
    await chamarFuncaoPaginada(true, isObserver);
  };

  const handleScroll = () => {
    const el = containerRef.current;
    if (!el) return;

    const y = el.scrollTop || 0;
    setScrollTop(y);
    if (y > 0) hasUserScrolledRef.current = true;
    onScrollChange?.(y);

    tryLoadNext(false);
  };

  const { elementRef: ultimoItemLista } = useIntersectionObserver({
    onIntersecting: () => {
      const el = containerRef.current;
      if (!el) return;
      const estáNoTopo = el.scrollTop === 0;
      const chegouPertoDoFim =
        el.scrollTop + el.clientHeight + 200 >=
        Math.min(el.scrollHeight, items.length * itemHeight);
      if (
        !isLoading &&
        (!totalRegistros || items.length < totalRegistros) &&
        !estáNoTopo &&
        chegouPertoDoFim
      ) {
        chamarFuncaoPaginada(true, true);
      }
    },
    enabled: true,
    getRoot: () => containerRef.current,
    rootMargin: '0px 0px 0px 0px',
    threshold: 0.01,
    cooldownMs: 400,
  });

  useImperativeHandle(ref, () => ({
    reload: () => chamarFuncaoPaginada(false, false),
  }));

  useEffect(() => {
    chamarFuncaoPaginada(true, false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useLayoutEffect(() => {
    if (containerRef.current && typeof initialScrollTop === 'number') {
      containerRef.current.scrollTop = initialScrollTop;
    }
  }, [initialScrollTop]);

  return (
    <>
      {(!isLoading || visibleItems.length > 0) && (
        <Box pos="relative" w="full">
          <Box
            pos="relative"
            minH={visibleItems.length === 0 ? '52px' : `${containerHeight}px`}
            overflowY="auto"
            overflowX="hidden"
            id="containerVirtualized"
            w="full"
            color="white"
            onScroll={handleScroll}
            mr="-10px"
            sx={{
              ...sx,
              '& > div': {
                marginRight: sx ? 'unset' : '6px',
              },

              '&::-webkit-scrollbar': {
                width: '8px',
                scrollbarWidth: 'thin',
              },

              '&::-webkit-scrollbar-track': {
                background: 'none',
                borderRadius: '3px',
              },

              '&::-webkit-scrollbar-thumb': {
                background: 'gray.200',
                borderRadius: '3px',
              },

              '&::-webkit-scrollbar-thumb:hover': {
                background: 'gray.200',
              },
            }}
            ref={containerRef}
          >
            <Box h={`${itemHeight * items.length}px`}>
              <Box color="currentColor" w="100%" position="relative">
                {visibleItems?.length > 0 ? (
                  <Box
                    pos="absolute"
                    top={`${startIndex * itemHeight}px`}
                    left="0"
                    right="0"
                    p="2px"
                  >
                    {render(
                      visibleItems,
                      `${valueItemHeight}px`,
                      `${marginBottom}px`
                    )}
                  </Box>
                ) : (
                  <>
                    {!isLoading && (
                      <Flex
                        color="black"
                        flexDirection={['column', 'row', 'row']}
                        justifyContent="space-between"
                        alignItems={['left', 'center', 'center']}
                        mb="5px"
                        h="48px"
                        bg="white"
                        p="10px"
                        pl={['2px', '20px', '20px']}
                        pr={['10px', '20px', '20px']}
                        borderRadius="6px"
                      >
                        {noItemsMessage}
                      </Flex>
                    )}
                  </>
                )}
              </Box>
            </Box>
            {handleOnClick && <Box h="12px" ref={ultimoItemLista} />}
          </Box>
        </Box>
      )}
    </>
  );
}

const VirtualizedList = forwardRef(List) as <T>(
  props: VirtualizedListProps<T> & {
    ref?: React.ForwardedRef<VirtualizedListRefProps>;
  }
) => ReturnType<typeof List>;

export default VirtualizedList;
