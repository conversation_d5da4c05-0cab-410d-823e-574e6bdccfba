import { useEffect, useRef, MutableRefObject } from 'react';

type UseIOProps = {
  onIntersecting: () => void;
  enabled?: boolean;
  getRoot?: () => Element | null;
  rootMargin?: string;
  threshold?: number | number[];
  cooldownMs?: number;
};

export function useIntersectionObserver<T extends Element = HTMLDivElement>({
  onIntersecting,
  enabled = true,
  getRoot,
  rootMargin,
  threshold,
  cooldownMs = 300,
}: UseIOProps) {
  const elementRef = useRef<T | null>(null);
  const cbRef = useRef(onIntersecting);
  const armedRef = useRef(true);
  const wasIntersectingRef = useRef(false);

  useEffect(() => {
    cbRef.current = onIntersecting;
  }, [onIntersecting]);

  useEffect(() => {
    if (!enabled) return;

    const root = getRoot?.() ?? null;
    const io = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        const now = entry.isIntersecting && entry.intersectionRatio > 0;

        if (now && !wasIntersectingRef.current && armedRef.current) {
          armedRef.current = false;
          cbRef.current();
          setTimeout(() => {
            armedRef.current = true;
          }, cooldownMs);
        }
        wasIntersectingRef.current = now;
      },
      { root, rootMargin, threshold }
    );

    const el = elementRef.current;
    if (el) io.observe(el);

    return () => io.disconnect();
  }, [enabled, getRoot, rootMargin, threshold, cooldownMs]);

  return { elementRef: elementRef as MutableRefObject<T | null> };
}
